"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MapPin, Calendar } from "lucide-react"
import type { Artist } from "../types/artist"

interface ArtistCardProps {
  artist: Artist
  onClick?: () => void
}

export function ArtistCard({ artist, onClick }: ArtistCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const getActiveYears = () => {
    if (!artist.formedDate) return "Unknown"
    try {
      const startYear = new Date(artist.formedDate).getFullYear()
      if (artist.disbandedDate) {
        const endYear = new Date(artist.disbandedDate).getFullYear()
        return `${startYear} - ${endYear}`
      }
      return `${startYear} - Present`
    } catch {
      return "Unknown"
    }
  }
  

  // const isActive = !artist.disbandedDate

  return (
    <Card
      className="group relative overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-lg bg-card border-border/50 hover:border-primary/20"
      onClick={onClick}
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <CardContent className="relative p-0 h-full">
        {/* Header section with avatar */}
        <div className="relative p-6 pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Avatar className="w-14 h-14 ring-2 ring-border group-hover:ring-primary/30 relative transition-all duration-300">
                 <div
              className="absolute inset-0 bg-cover bg-center blur-md scale-110"
              style={{
                backgroundImage: `url(${artist.profileImage || "/dummy-image.png"})`
              }}
            />
                  <AvatarImage
                    src={artist.profileImage && artist.profileImage.trim() ? (artist.profileImage.trim().startsWith('http') ? artist.profileImage.trim() : `/${artist.profileImage.trim()}`) : "/dummy-image.png"}
                    alt={artist.name}
              className="object-contain w-full h-full relative z-10"

                  />
                  <AvatarFallback className="bg-primary/10 text-primary font-semibold text-lg">
                    {getInitials(artist.name)}
                  </AvatarFallback>
                </Avatar>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-lg text-card-foreground truncate group-hover:text-primary transition-colors">
                    {artist.name}
                  </h3>
                  {/* <Badge
                    variant={isActive ? "default" : "secondary"}
                    className={`text-xs px-2 py-0.5 ${
                      isActive
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    {isActive ? 'Active' : 'Disbanded'}
                  </Badge> */}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bio section */}
        <div className="px-6 pb-4">
          <p className="text-muted-foreground text-sm leading-relaxed line-clamp-2 min-h-[2.5rem]">
            {artist.bio || "No biography available for this artist."}
          </p>
                {/* <Link href={`/artist/?id=${artist.id}`} >
            <Button className="gap-2 group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
              <ExternalLink className="w-4 h-4" />
              View Profile
            </Button>
          </Link> */}
        </div>

        {/* Footer with metadata */}
        <div className="px-6 py-4 bg-muted/30 border-t border-border/50">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1.5 min-w-0 flex-1">
              <MapPin className="w-3.5 h-3.5 flex-shrink-0" />
              <span className="truncate">{artist.location || "Unknown location"}</span>
            </div>
            <div className="flex items-center gap-1.5 ml-4">
              <Calendar className="w-3.5 h-3.5 flex-shrink-0" />
              <span className="whitespace-nowrap">{getActiveYears()}</span>
            </div>
          </div>
        </div>

        </CardContent>
    </Card>
  )
}
